import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get query parameters
    const url = new URL(request.url)
    const productId = url.searchParams.get('productId')
    const packageId = url.searchParams.get('packageId')
    
    // First try to get tenant from headers (set by middleware)
    let tenantId = request.headers.get('x-tenant-id')
    
    // If no tenant in headers, try to get from user profile
    if (!tenantId) {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('tenant_id')
            .eq('id', user.id)
            .single()
          
          tenantId = profile?.tenant_id
        }
      } catch (authError) {
        // No authenticated user, will use main tenant fallback
      }
    }
    
    // If still no tenant, get main tenant as fallback
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()
      
      tenantId = mainTenant?.id || 'caf1844f-4cc2-4c17-a775-1c837ae01051'
    }

    // Build query for custom fields
    let customFieldsQuery = supabase
      .from('custom_fields')
      .select(`
        id,
        label,
        field_type,
        description,
        required,
        placeholder,
        field_order,
        validation_rules,
        display_options,
        type
      `)
      .eq('tenant_id', tenantId)
      .order('field_order', { ascending: true })

    // Filter by product or package
    if (productId) {
      customFieldsQuery = customFieldsQuery.eq('product_id', productId)
    } else if (packageId) {
      customFieldsQuery = customFieldsQuery.eq('package_id', packageId)
    }

    const { data: customFields, error } = await customFieldsQuery

    if (error) {
      console.error('Error fetching custom fields:', error)
      return NextResponse.json({ error: 'Failed to fetch custom fields' }, { status: 500 })
    }

    return NextResponse.json({
      customFields: customFields || [],
      success: true
    })

  } catch (error) {
    console.error('Custom fields API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
