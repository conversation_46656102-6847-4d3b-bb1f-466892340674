import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { z } from 'zod'
import { cacheInvalidation } from '../../../../lib/optimizedQueries'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Validation schema for updates
const productUpdateSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(10).max(2000).optional(),
  cover_image: z.string().url().optional(),
  category: z.string().min(1).optional(),
  category_id: z.string().uuid().optional(),
  tags: z.array(z.string()).optional(),
  featured: z.boolean().optional(),
  // Product-level pricing
  original_price: z.number().min(0).optional(),
  user_price: z.number().min(0).optional(),
  discount_price: z.number().min(0).optional(),
  distributor_price: z.number().min(0).optional(),
  packages: z.array(z.object({
    id: z.string().uuid().optional(),
    name: z.string().min(1),
    // Legacy price field for backward compatibility
    price: z.number().min(0).optional(),
    // Enhanced pricing fields
    original_price: z.number().min(0),
    user_price: z.number().min(0),
    discount_price: z.number().min(0).optional(),
    distributor_price: z.number().min(0).optional(),
    image: z.string().url().optional(),
    description: z.string().optional(),
    use_product_image: z.boolean().default(false),
    image_reference_type: z.enum(['url', 'product_image']).default('url'),
    has_digital_codes: z.boolean().default(false),
    // Manual inventory management fields
    manual_quantity: z.number().min(0, "الكمية اليدوية لا يمكن أن تكون سالبة").optional(),
    track_inventory: z.boolean().default(false),
    unlimited_stock: z.boolean().default(false)
  }).refine((data) => data.user_price > data.original_price, {
    message: "سعر المستخدم يجب أن يكون أكبر من السعر الأصلي",
    path: ["user_price"]
  }).refine((data) => !data.discount_price || data.discount_price < data.user_price, {
    message: "سعر الخصم يجب أن يكون أقل من سعر المستخدم",
    path: ["discount_price"]
  }).refine((data) => !data.discount_price || data.discount_price > data.original_price, {
    message: "سعر الخصم يجب أن يكون أكبر من السعر الأصلي",
    path: ["discount_price"]
  }).refine((data) => !data.discount_price || data.discount_price !== data.user_price, {
    message: "سعر الخصم لا يمكن أن يساوي سعر المستخدم",
    path: ["discount_price"]
  }).refine((data) => !data.distributor_price || data.distributor_price < data.user_price, {
    message: "سعر الموزع يجب أن يكون أقل من سعر المستخدم",
    path: ["distributor_price"]
  })).optional(),
  // Enhanced custom fields and dropdowns
  customFields: z.array(z.object({
    id: z.string().optional(),
    label: z.string().min(1),
    field_type: z.enum(['text', 'email', 'password', 'number', 'tel', 'url', 'textarea']),
    description: z.string().optional(),
    required: z.boolean().default(false),
    placeholder: z.string().optional(),
    field_order: z.number().default(0),
    validation_rules: z.object({
      pattern: z.string().optional(),
      minLength: z.number().optional(),
      maxLength: z.number().optional(),
      min: z.number().optional(),
      max: z.number().optional()
    }).optional(),
    display_options: z.object({
      icon: z.string().optional(),
      helpText: z.string().optional(),
      showStrength: z.boolean().optional(),
      rows: z.number().optional()
    }).optional()
  })).optional(),
  dropdowns: z.array(z.object({
    id: z.string().optional(),
    label: z.string().min(1),
    description: z.string().optional(),
    required: z.boolean().default(false),
    field_order: z.number().default(0),
    validation_rules: z.object({
      allowMultiple: z.boolean().optional(),
      minSelections: z.number().optional(),
      maxSelections: z.number().optional()
    }).optional(),
    display_options: z.object({
      icon: z.string().optional(),
      helpText: z.string().optional(),
      searchable: z.boolean().optional(),
      placeholder: z.string().optional()
    }).optional(),
    options: z.array(z.object({
      id: z.string().optional(),
      label: z.string().min(1),
      description: z.string().optional(),
      display_order: z.number().default(0),
      is_default: z.boolean().default(false)
    }))
  })).optional()
})

// Sanitization function
function sanitizeHtml(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

// PUT /api/admin/products/[id] - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Verify product exists and belongs to tenant
    const { data: existingProduct, error: productCheckError } = await supabase
      .from('products')
      .select('id, slug, tenant_id')
      .eq('id', params.id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (productCheckError || !existingProduct) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    const body = await request.json()
    
    // Sanitize input data
    const sanitizedData = {
      ...body,
      title: body.title ? sanitizeHtml(body.title) : undefined,
      description: body.description ? sanitizeHtml(body.description) : undefined,
      category: body.category ? sanitizeHtml(body.category) : undefined
    }

    const validatedData = productUpdateSchema.parse(sanitizedData)

    // Update slug if title changed
    let updateData: any = { ...validatedData }
    if (validatedData.title) {
      const newSlug = validatedData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()

      // Check if new slug conflicts with existing products (excluding current)
      if (newSlug !== existingProduct.slug) {
        const { data: conflictingProduct } = await supabase
          .from('products')
          .select('id')
          .eq('slug', newSlug)
          .eq('tenant_id', profile.tenant_id)
          .neq('id', params.id)
          .single()

        if (conflictingProduct) {
          return NextResponse.json({ error: 'منتج بهذا الاسم موجود بالفعل' }, { status: 409 })
        }

        updateData.slug = newSlug
      }
    }

    // Remove packages from product update data
    const { packages, ...productUpdateData } = updateData

    // Update product
    const { data: updatedProduct, error: updateError } = await supabase
      .from('products')
      .update({
        ...productUpdateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (updateError) {
      console.error('Product update error:', updateError)
      return NextResponse.json({ error: 'Failed to update product', details: updateError.message }, { status: 500 })
    }

    // Handle packages update if provided
    if (packages) {
      try {
        // Delete existing packages for this product
        const { error: deleteError } = await supabase
          .from('packages')
          .delete()
          .eq('product_id', params.id)
          .eq('tenant_id', profile.tenant_id)

        if (deleteError) {
          console.error('Error deleting existing packages:', deleteError)
          return NextResponse.json({
            error: 'Failed to update packages',
            details: deleteError.message
          }, { status: 500 })
        }

        // Insert new packages if any provided
        if (packages.length > 0) {
          const packagesData = packages.map(pkg => ({
            tenant_id: profile.tenant_id,
            product_id: params.id,
            name: sanitizeHtml(pkg.name),
            // Keep legacy price field for backward compatibility
            price: pkg.user_price || pkg.price || 0,
            // Enhanced pricing fields
            original_price: pkg.original_price,
            user_price: pkg.user_price,
            discount_price: pkg.discount_price,
            distributor_price: pkg.distributor_price,
            image: pkg.image || '',
            description: pkg.description ? sanitizeHtml(pkg.description) : null,
            use_product_image: pkg.use_product_image || false,
            image_reference_type: pkg.image_reference_type || 'url',
            has_digital_codes: pkg.has_digital_codes || false,
            // Manual inventory management fields
            manual_quantity: pkg.manual_quantity || 0,
            track_inventory: pkg.track_inventory || false,
            unlimited_stock: pkg.unlimited_stock || false
          }))

          const { error: packagesError } = await supabase
            .from('packages')
            .insert(packagesData)

          if (packagesError) {
            console.error('Packages creation error:', packagesError)
            return NextResponse.json({
              error: 'Failed to create new packages',
              details: packagesError.message
            }, { status: 500 })
          }
        }
      } catch (error) {
        console.error('Error updating packages:', error)
        return NextResponse.json({
          error: 'Failed to update packages',
          details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 })
      }
    }

    // Invalidate product cache after successful update
    cacheInvalidation.onProductChange(profile.tenant_id)

    return NextResponse.json({
      success: true,
      product: updatedProduct,
      message: 'تم تحديث المنتج بنجاح'
    })

  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }

  } catch (error) {
    console.error('Error in PUT /api/admin/products/[id]:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'بيانات غير صالحة',
        details: error.errors.map(e => e.message).join(', ')
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE /api/admin/products/[id] - Delete product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Verify product exists and belongs to tenant
    const { data: existingProduct, error: productCheckError } = await supabase
      .from('products')
      .select('id, title')
      .eq('id', params.id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (productCheckError || !existingProduct) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    // Check if product has orders (prevent deletion if it does)
    const { data: orders, error: ordersCheckError } = await supabase
      .from('orders')
      .select('id')
      .eq('product_id', params.id)
      .eq('tenant_id', profile.tenant_id)
      .limit(1)

    if (ordersCheckError) {
      console.error('Orders check error:', ordersCheckError)
      return NextResponse.json({ error: 'Failed to check product orders' }, { status: 500 })
    }

    if (orders && orders.length > 0) {
      return NextResponse.json({
        error: 'لا يمكن حذف منتج يحتوي على طلبات. يرجى إلغاء تفعيل المنتج بدلاً من حذفه.'
      }, { status: 409 })
    }

    // Delete packages first (foreign key constraint)
    const { error: packagesDeleteError } = await supabase
      .from('packages')
      .delete()
      .eq('product_id', params.id)
      .eq('tenant_id', profile.tenant_id)

    if (packagesDeleteError) {
      console.error('Packages deletion error:', packagesDeleteError)
      return NextResponse.json({ error: 'Failed to delete product packages', details: packagesDeleteError.message }, { status: 500 })
    }

    // Delete product
    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .eq('id', params.id)
      .eq('tenant_id', profile.tenant_id)

    if (deleteError) {
      console.error('Product deletion error:', deleteError)
      return NextResponse.json({ error: 'Failed to delete product', details: deleteError.message }, { status: 500 })
    }

    // Invalidate product cache after successful deletion
    cacheInvalidation.onProductChange(profile.tenant_id)

    return NextResponse.json({
      success: true,
      message: 'تم حذف المنتج بنجاح'
    })

  } catch (error) {
    console.error('Error in DELETE /api/admin/products/[id]:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
